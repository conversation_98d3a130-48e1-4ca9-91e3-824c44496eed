# 导入PyTorch和相关库
import torch
import torch.nn as nn
import torch.optim as optim
import torch.nn.functional as F
from torch.utils.data import DataLoader, TensorDataset

# 导入数据处理和可视化库
import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns
from sklearn.model_selection import KFold
from sklearn.preprocessing import StandardScaler
import pandas as pd
from collections import defaultdict
import warnings
warnings.filterwarnings('ignore')

# 设置随机种子以确保结果可重现
torch.manual_seed(42)
np.random.seed(42)

# 设置matplotlib参数
plt.rcParams['figure.figsize'] = (12, 8)
plt.rcParams['font.size'] = 12
plt.style.use('seaborn')

print("✅ All libraries imported successfully!")
print(f"PyTorch version: {torch.__version__}")
print(f"Device available: {'CUDA' if torch.cuda.is_available() else 'CPU'}")


class MLP(nn.Module):
    """
    多层感知机(MLP)类 - 支持1D和2D输入的回归任务

    架构:
    - 3个隐藏层，每层16个神经元
    - ReLU激活函数（隐藏层）
    - 无激活函数（输出层）
    - 1维输出
    """

    def __init__(self, input_dim):
        """
        初始化MLP网络

        Args:
            input_dim (int): 输入维度 (1 或 2)
        """
        super(MLP, self).__init__()

        # 定义网络层
        self.fc1 = nn.Linear(input_dim, 16)  # 第一个隐藏层：输入 -> 16个神经元
        self.fc2 = nn.Linear(16, 16)         # 第二个隐藏层：16 -> 16个神经元
        self.fc3 = nn.Linear(16, 16)         # 第三个隐藏层：16 -> 16个神经元
        self.fc4 = nn.Linear(16, 1)          # 输出层：16 -> 1个输出

        # 初始化权重
        self._initialize_weights()

    def _initialize_weights(self):
        """使用Xavier初始化权重"""
        for module in self.modules():
            if isinstance(module, nn.Linear):
                nn.init.xavier_uniform_(module.weight)  # Xavier均匀初始化
                nn.init.constant_(module.bias, 0.0)     # 偏置初始化为0

    def forward(self, x):
        """
        前向传播

        Args:
            x (torch.Tensor): 输入张量，形状为 (batch_size, input_dim)

        Returns:
            torch.Tensor: 输出张量，形状为 (batch_size, 1)
        """
        # 第一个隐藏层 + ReLU激活
        x = F.relu(self.fc1(x))

        # 第二个隐藏层 + ReLU激活
        x = F.relu(self.fc2(x))

        # 第三个隐藏层 + ReLU激活
        x = F.relu(self.fc3(x))

        # 输出层（无激活函数）
        x = self.fc4(x)

        return x

    def count_parameters(self):
        """计算模型参数数量"""
        return sum(p.numel() for p in self.parameters() if p.requires_grad)

# 测试MLP类
print("Testing MLP class...")

# 测试1D输入
mlp_1d = MLP(input_dim=1)
test_input_1d = torch.randn(5, 1)  # 5个样本，1维输入
output_1d = mlp_1d(test_input_1d)
print(f"1D Input shape: {test_input_1d.shape}")
print(f"1D Output shape: {output_1d.shape}")

# 测试2D输入
mlp_2d = MLP(input_dim=2)
test_input_2d = torch.randn(5, 2)  # 5个样本，2维输入
output_2d = mlp_2d(test_input_2d)
print(f"2D Input shape: {test_input_2d.shape}")
print(f"2D Output shape: {output_2d.shape}")

print(f"Model parameters (1D): {mlp_1d.count_parameters()}")
print(f"Model parameters (2D): {mlp_2d.count_parameters()}")
print("✅ MLP class implementation successful!")


def overfitting_experiment_1d():
    """
    1D输入的过拟合实验
    使用不同的训练集大小来展示过拟合现象
    """
    print("🔬 Starting 1D Overfitting Experiment...")

    # 实验参数设置
    ndims = 1
    ntest = 500
    num_seeds = 5
    num_epochs = 800
    batch_size = 16

    # 不同的训练集大小 - 精心选择以展示过拟合程度
    train_sizes = [20, 50, 100, 200, 500]  # 从强过拟合到无过拟合

    # 存储结果
    all_results = {}

    # 为每个训练集大小进行实验
    for ntrain in train_sizes:
        print(f"\n📊 Training with {ntrain} samples...")

        # 生成数据
        x_train, y_train, x_test, y_test = generate_data(ntrain, ntest, ndims, seed=42)

        # 创建模型和训练器
        model = MLP(input_dim=ndims)
        trainer = AdvancedMLPTrainer(model)

        # 多种子训练
        results = trainer.train_multiple_seeds(
            x_train, y_train, x_test, y_test,
            num_seeds=num_seeds,
            optimizer_class=optim.SGD,
            optimizer_params={'lr': 0.01},
            num_epochs=num_epochs,
            batch_size=batch_size
        )

        all_results[ntrain] = results

        # 计算平均性能
        final_train_losses = [r['final_train_loss'] for r in results]
        final_test_losses = [r['final_test_loss'] for r in results]

        print(f"   Train Loss: {np.mean(final_train_losses):.6f} ± {np.std(final_train_losses):.6f}")
        print(f"   Test Loss:  {np.mean(final_test_losses):.6f} ± {np.std(final_test_losses):.6f}")
        print(f"   Overfitting Gap: {np.mean(final_test_losses) - np.mean(final_train_losses):.6f}")

    # 可视化结果
    visualize_overfitting_results_1d(all_results, train_sizes)

    return all_results

def visualize_overfitting_results_1d(all_results, train_sizes):
    """可视化1D过拟合实验结果"""

    fig, axes = plt.subplots(2, 3, figsize=(18, 12))
    fig.suptitle('1D Input Overfitting Analysis', fontsize=16, fontweight='bold')

    # 颜色映射
    colors = plt.cm.viridis(np.linspace(0, 1, len(train_sizes)))

    # 1. 学习曲线对比
    ax1 = axes[0, 0]
    for i, ntrain in enumerate(train_sizes):
        results = all_results[ntrain]

        # 计算平均学习曲线
        all_train_losses = np.array([r['train_losses'] for r in results])
        all_test_losses = np.array([r['test_losses'] for r in results])

        mean_train = np.mean(all_train_losses, axis=0)
        mean_test = np.mean(all_test_losses, axis=0)
        std_train = np.std(all_train_losses, axis=0)
        std_test = np.std(all_test_losses, axis=0)

        epochs = range(len(mean_train))

        # 绘制训练损失
        ax1.plot(epochs, mean_train, color=colors[i], linestyle='-',
                label=f'Train (N={ntrain})', alpha=0.8)
        ax1.fill_between(epochs, mean_train - std_train, mean_train + std_train,
                        color=colors[i], alpha=0.2)

        # 绘制测试损失
        ax1.plot(epochs, mean_test, color=colors[i], linestyle='--',
                label=f'Test (N={ntrain})', alpha=0.8)
        ax1.fill_between(epochs, mean_test - std_test, mean_test + std_test,
                        color=colors[i], alpha=0.2)

    ax1.set_xlabel('Epoch')
    ax1.set_ylabel('Loss')
    ax1.set_title('Learning Curves Comparison')
    ax1.legend(bbox_to_anchor=(1.05, 1), loc='upper left')
    ax1.grid(True, alpha=0.3)
    ax1.set_yscale('log')

    # 2. 最终损失对比
    ax2 = axes[0, 1]
    final_train_means = []
    final_test_means = []
    final_train_stds = []
    final_test_stds = []

    for ntrain in train_sizes:
        results = all_results[ntrain]
        train_losses = [r['final_train_loss'] for r in results]
        test_losses = [r['final_test_loss'] for r in results]

        final_train_means.append(np.mean(train_losses))
        final_test_means.append(np.mean(test_losses))
        final_train_stds.append(np.std(train_losses))
        final_test_stds.append(np.std(test_losses))

    x_pos = np.arange(len(train_sizes))
    width = 0.35

    ax2.bar(x_pos - width/2, final_train_means, width, yerr=final_train_stds,
           label='Training Loss', alpha=0.8, capsize=5)
    ax2.bar(x_pos + width/2, final_test_means, width, yerr=final_test_stds,
           label='Test Loss', alpha=0.8, capsize=5)

    ax2.set_xlabel('Training Set Size')
    ax2.set_ylabel('Final Loss')
    ax2.set_title('Final Loss vs Training Set Size')
    ax2.set_xticks(x_pos)
    ax2.set_xticklabels(train_sizes)
    ax2.legend()
    ax2.grid(True, alpha=0.3)

    # 3. 过拟合程度分析
    ax3 = axes[0, 2]
    overfitting_gaps = np.array(final_test_means) - np.array(final_train_means)

    bars = ax3.bar(train_sizes, overfitting_gaps, color=colors, alpha=0.7)
    ax3.set_xlabel('Training Set Size')
    ax3.set_ylabel('Overfitting Gap (Test - Train)')
    ax3.set_title('Overfitting Degree Analysis')
    ax3.grid(True, alpha=0.3)

    # 在柱状图上添加数值
    for bar, gap in zip(bars, overfitting_gaps):
        height = bar.get_height()
        ax3.text(bar.get_x() + bar.get_width()/2., height + 0.001,
                f'{gap:.4f}', ha='center', va='bottom')

    # 4. 详细学习曲线（选择几个代表性大小）
    selected_sizes = [20, 100, 500]  # 强、中、弱过拟合
    ax4 = axes[1, 0]

    for ntrain in selected_sizes:
        results = all_results[ntrain]
        all_train_losses = np.array([r['train_losses'] for r in results])
        all_test_losses = np.array([r['test_losses'] for r in results])

        mean_train = np.mean(all_train_losses, axis=0)
        mean_test = np.mean(all_test_losses, axis=0)

        epochs = range(len(mean_train))
        ax4.plot(epochs, mean_train, label=f'Train (N={ntrain})', linestyle='-')
        ax4.plot(epochs, mean_test, label=f'Test (N={ntrain})', linestyle='--')

    ax4.set_xlabel('Epoch')
    ax4.set_ylabel('Loss')
    ax4.set_title('Detailed Learning Curves (Selected Sizes)')
    ax4.legend()
    ax4.grid(True, alpha=0.3)
    ax4.set_yscale('log')

    # 5. 收敛速度分析
    ax5 = axes[1, 1]
    convergence_epochs = []

    for ntrain in train_sizes:
        results = all_results[ntrain]
        epochs_to_converge = []

        for result in results:
            train_losses = result['train_losses']
            # 定义收敛为损失变化小于阈值
            for i in range(50, len(train_losses)):
                if abs(train_losses[i] - train_losses[i-10]) < 0.001:
                    epochs_to_converge.append(i)
                    break
            else:
                epochs_to_converge.append(len(train_losses))

        convergence_epochs.append(np.mean(epochs_to_converge))

    ax5.plot(train_sizes, convergence_epochs, 'o-', linewidth=2, markersize=8)
    ax5.set_xlabel('Training Set Size')
    ax5.set_ylabel('Epochs to Convergence')
    ax5.set_title('Convergence Speed Analysis')
    ax5.grid(True, alpha=0.3)

    # 6. 方差分析
    ax6 = axes[1, 2]
    train_variances = final_train_stds
    test_variances = final_test_stds

    ax6.plot(train_sizes, train_variances, 'o-', label='Training Loss Variance', linewidth=2)
    ax6.plot(train_sizes, test_variances, 's-', label='Test Loss Variance', linewidth=2)
    ax6.set_xlabel('Training Set Size')
    ax6.set_ylabel('Loss Standard Deviation')
    ax6.set_title('Loss Variance Analysis')
    ax6.legend()
    ax6.grid(True, alpha=0.3)

    plt.tight_layout()
    plt.show()

    # 打印分析结果
    print("\n📈 1D OVERFITTING ANALYSIS RESULTS:")
    print("=" * 50)

    for i, ntrain in enumerate(train_sizes):
        gap = overfitting_gaps[i]
        if gap > 0.05:
            status = "🔴 Strong Overfitting"
        elif gap > 0.02:
            status = "🟡 Moderate Overfitting"
        else:
            status = "🟢 Little/No Overfitting"

        print(f"N={ntrain:3d}: Gap={gap:.4f} - {status}")

    # 找到无过拟合的最小数据集大小
    no_overfitting_threshold = 0.02
    min_size_no_overfitting = None

    for i, gap in enumerate(overfitting_gaps):
        if gap <= no_overfitting_threshold:
            min_size_no_overfitting = train_sizes[i]
            break

    if min_size_no_overfitting:
        print(f"\n🎯 Minimum dataset size with no overfitting: {min_size_no_overfitting}")
    else:
        print(f"\n⚠️  No dataset size achieved no overfitting (threshold: {no_overfitting_threshold})")

# 运行1D过拟合实验
results_1d = overfitting_experiment_1d()


###
### Add your code to complete Subtask 1F here
###

###
### Add your code to complete Subtask 1H here
###

###
### Add your code to complete Subtask 1K here
### The code here must include an updated version of the code used for subtask 1A, as well as the rest of the code needed to perform the experiment and to plot the results.
###