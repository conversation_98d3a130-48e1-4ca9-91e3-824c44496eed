{"cells": [{"cell_type": "markdown", "metadata": {"id": "BkCHccyuG_Kg"}, "source": ["# Machine Learning Assessment, 2025, Part 2 <ignore>\n", "## Overview\n", "The overarching goal of this assignment is to demonstrate your understanding of the use of Neural Networks (NNs) to solve regression and classification tasks. You will be asked to carry out two tasks, each of which will contain subtasks requiring a mixture of short and long responses from you. There are 163 marks available in total. This will be converted into a percentage.\n", "\n", "## Instructions\n", "### Code and Markdown cells\n", "All of your report, including code and Markdown/text, ***must*** be written up in ***this*** notebook. Please make sure you change the title of this file so that XXXXXX is replaced by your candidate number. You can use code cells, where provided, to write code to implement, train, test, and analyse your NNs, as well as to generate figures to plot data and the results of your experiments. If you wish to add more code cells, you may do so immediately after the code cells that have been provided, but do not add any code cells anywhere else in this file. You must use the Markdown/text cells that are provided for verbose responses to the subtasks, and have \"*Replace this text with...*\" in italics, replacing that text with your own text. So that we can mark your reports with greater consistency, please ***do not***:\n", "\n", "* add any of your own Markdown cells.\n", "* rearrange the sequence of cells in this notebook.\n", "* delete any cells, including the ones explaining what you need to do.\n", "\n", "### Plotting figures\n", "All plots of data ***must*** be produced as output from a code cell. Any plots that have been imported as images, rather than being the output of a plot function (such as that provided by matplotlib) will not receive marks. This is to ensure that you did the work to produce that plot. Diagrams, e.g. of your network architecture, can be imported as images. Any plots should have an accompanying caption that states the Figure number and a detailed description of what the figure shows (see the guidenace in the file ```useful_code_for_figures_equations.ipynb```). \n", "\n", "### Writing code\n", "Please provide verbose comments throughout your code so that it is easy for us to interpret what you are attempting to achieve. Long comments are useful at the beginning of a block of code. Short comments, e.g. to explain the purpose of a new variable, or one of several steps in some analyses, are useful on every few lines of code, if not on every line. Please do not use the code cells for writing verbose responses to subtasks, which should instead be written in the provided Markdown cells."]}, {"cell_type": "markdown", "metadata": {"id": "HAAMLJvMBOnn"}, "source": ["# TASK 1 - 93 marks available <ignore>"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Subtask 1A - 5 marks <ignore>\n", "Using Pytorch, create a class that defines a multi-layer perceptron (MLP). The MLP must:\n", "* take inputs that are either 1-dimensional or 2-dimensional\n", "* have three hidden layers with 16 neurons in each layer, and a 1-dimensional output\n", "* use ReLU non-linear activations for the hidden layers, and no nonlinearity for the output layer. \n", "\n", "The class must include a forward pass function that takes an input, X, and returns an output, Y, where X is a MxN tensor (M samples, N input dimensions), and Y is a Mx1 tensor. Thus, each N-dimensional input sample is paired with a scalar output.\n", "\n", "Use the code cell below to create your model. For each line of code, write a short comment to explain what the code does."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["###\n", "### Use this code cell to create the MLP\n", "###"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Subtask 1B - 3 marks <ignore>\n", "Provide three reasons why the ReLU nonlinearity might be a better choice than a Sigmoid nonlinearity for a regression task."]}, {"cell_type": "markdown", "metadata": {}, "source": ["*Replace this text with your response*"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Subtask 1C - 2 marks <ignore>\n", "Data for Task 1 is to be generate using the following equation:\n", "$$\n", "y_i = 0.2\\left(x_i - 3\\right)^2 + 0.2\\xi_i\n", "$$\n", "where <lt>$i$ denotes the sample and <lt>$\\xi$ is a random variable drawn from a normal distribution with zero mean and unit variance. What is the optimal loss function to perform regression on pairs of (x,y) samples generated by this function? Why? "]}, {"cell_type": "markdown", "metadata": {}, "source": ["*Replace this text with your response*"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Subtask 1D - 8 marks <ignore>\n", "Name two gradient descent optimisers that were discussed in the lectures, and that might be used for this regression task. Describe an advantage and a disadvantage for using each, providing reasons."]}, {"cell_type": "markdown", "metadata": {}, "source": ["*Replace this text with your response*"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Subtask 1E - 6 marks <ignore>\n", "What is meant by the term \"batch\" when training a NN? What is the smallest and the largest possible batch sizes? What are the advantages of using small batch sizes? What are the advantages of using large batch sizes?"]}, {"cell_type": "markdown", "metadata": {}, "source": ["*Replace this text with your response*"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Subtask 1F - 16 marks <ignore>\n", "Using the model you built in 1A, and data generated by the code below, demonstrate how the size of the training dataset affects the amount of overfitting when the input data is 1-dimensional. Your evidence should come in the form of learning curves, i.e. plotting loss as a function of training episodes. These learning curves should appear similar to the ones you were shown in the lectures. You must use at least 5 different training dataset sizes to demonstrate three scenarios and the transitions between them: 1) strong overfitting, 2) intermediate overfitting, 3) little to no overfitting. For each dataset size, rerun the model using a different random seed at least 5 times. The learning curves you plot must show the mean loss, when averaged across reruns. The output of your code cell must include these figures, and the figures must be generated by code that is executed in the cell. You must not save figures and then import them. \n", "\n", "**Note 1**: you may have to experiment with various different dataset sizes before you select the 5 or more sizes that effectively demonstrate strong, intermediate, and no overfitting. Choose these training dataset sizes wisely, as you will be required to compare results to a new scenario in subtask 1H.\n", "\n", "**Note 2**: Use the stochastic gradient descent optimizer, rather than more sophisticated optimizers."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["###\n", "### Use this code to generate the data\n", "###\n", "\n", "# Funtion definition\n", "def func(x,ndims):\n", "    y = 0. # initialise y\n", "    for i in range(ndims): # for each input dimension\n", "        y += (0.2 * (x[:,i] - 3.0)) ** 2 # increment y value\n", "    y += torch.randn(y.size()) * 0.2 # add normally distributed noise\n", "    return y\n", "\n", "# Define parameters and generate data\n", "######################################################################\n", "######################################################################\n", "# PROVIDE VALUES FOR THESE VARIABLES ONLY. \n", "# LEAVE ALL OTHER VARIABLES UNTOUCHED\n", "ndims = \n", "Ntrain = \n", "Nbatch = \n", "######################################################################\n", "######################################################################\n", "Ntest = 500\n", "x_train = torch.rand(Ntrain, ndims)*10.0  # Inputs from 0 to 10\n", "y_train = func(x_train, ndims) # generate noisey y training data\n", "y_train = y_train.view(-1, 1) # reshape y data\n", "x_test = torch.rand(Ntest, ndims)*10.0  # Inputs from 0 to 10\n", "y_test = func(x_test, ndims) # generate noisey y test data\n", "y_test = y_test.view(-1, 1) # reshape y data"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["###\n", "### Add your code to complete Subtask 1F here\n", "###"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Subtask 1G - 6 marks <ignore>\n", "Describe the results you present in 1F, making reference to the figures you produced. In your description, include a statement of the smallest training dataset size at which overfitting is no longer seen. Finally, explain why it might be possible to see a gap between the training and test losses even if the model is not overfitting the training data, and describe a simple test for this scenario. "]}, {"cell_type": "markdown", "metadata": {}, "source": ["*Replace this text with your response*"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Subtask 1H - 13 marks <ignore>\n", "Repeat subtask 1F, but for 2-dimensional inputs. Make sure that you use suitable dataset training sizes to demonstrate the transition from strong overfitting to intermediate overfitting to no overfitting. You must include the smallest dataset size at which no overfitting is seen in subtask 1F. Remember, you must produce figures, just as you did for subtask 1F."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["###\n", "### Add your code to complete Subtask 1H here\n", "###"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Subtask 1I - 6 marks <ignore>\n", "Describe how increasing the dimensionality of the inputs affects the degree of overfitting by the model, making reference to the figures you produced in Subtask 1H. Provide reasons for why this is happening. In you answer, state whether there is an increase / a decrease / no change in the smallest dataset size that produces no overfitting, when going from 1-dimensional inputs to 2-dimensional inputs. "]}, {"cell_type": "markdown", "metadata": {}, "source": ["*Replace this text with your response*"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Subtask 1J - 16 marks <ignore>\n", "Replicate the model used in subtask 1A in the code cell below, adding weight decay as a regulariser. Using the 2-dimensional input data, and a training dataset size that produced overfitting, investigate how the weight decay rate affects overfitting and generalisability of the model. You must use at least 5 different decay rates to demonstrate the transition from strong overfitting to intermediate overfitting to little/no overfitting. For each decay rate, rerun the model using a different random seed at least 5 times. The learning curves you plot must show the mean loss, when averaged across reruns. The output of your code cell must include these figures, and the figures must be generated by code that is executed in the cell. You must not save figures and then import them. \n", "\n", "**Note**: you may have to experiment with various decay rates before you select the 5 or more decay rates that effectively demonstrate strong, intermediate, and no overfitting."]}, {"cell_type": "code", "execution_count": 2, "metadata": {}, "outputs": [], "source": ["###\n", "### Add your code to complete Subtask 1K here\n", "### The code here must include an updated version of the code used for subtask 1A, as well as the rest of the code needed to perform the experiment and to plot the results.\n", "###"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Subtask 1K - 6 marks <ignore>\n", "Describe how increasing the decay rate affects overfitting, making reference to the figures you produced in Subtask 1K. Describe any other effects you observe as a consequence of using weight decay, and suggest reasons as to why they occur. "]}, {"cell_type": "markdown", "metadata": {}, "source": ["*Replace this text with your response*"]}], "metadata": {"colab": {"provenance": []}, "kernelspec": {"display_name": "venv", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.8.16"}}, "nbformat": 4, "nbformat_minor": 0}