{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["# Useful code snippets\n", "Below are some snippets of Python and Markdown code that you may find useful for this report. "]}, {"cell_type": "markdown", "metadata": {"id": "vZtB7mFVRpyk"}, "source": ["## Entering LaTeX, e.g. for equations\n", "LaTeX can be used to format equations in these notebooks. To include an equation inline with the text, enter the LaTeX commands between two \"`$`\" symbols. For example, the LaTeX command `<lt>$y = mc + c$` produces <lt>$y = mx + c$ on the same line. The <lt> tag is important, as the word count script uses it to recognise the `<lt>$......$` formatting as LaTeX and exclude its contribution to the word count. To push the equation onto its own, separate line, enter the LaTeX commands between two \"`$$`\" symbols. For example, the LaTeX command: \n", "```\n", "<lt>$$L = - \\sum_{i=1}^{N_{\\rm samples}}\\sum_{c=1}^{N_{\\rm class}} \\mathbb{I}(y_i = c) \\log\\left(p\\left( y_i \\vert x_i\\right)\\right)$$\n", "```\n", "generates this equation:\n", "<lt>$$ L = - \\frac{1}{N_{\\rm samples}}\\sum_{i=1}^{N_{\\rm samples}}\\sum_{c=1}^{N_{\\rm class}} \\mathbb{I}(y_i = c) \\log\\left(p\\left( y_i \\vert x_i\\right)\\right)$$"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Producing figures \n", "***Please make sure all figures are clear and are large enough to see any important small details.*** All figures must have a caption that explains, with sufficient detail, what is being shown in the figure. All figures showing results from your experiments must be generated using code that takes, as input, data produced by your model. ***I will not run any of your code, so all figures must be visible*** in the Notebook when you submit it on Canvas. "]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Figures for plotting data/results from your model\n", "Below are snippets of code you can use in order to produce decent plots of your data. There are plenty of additional plotting functions and styles if you search for them online. The code below produces an example plot of a linear function with noise."]}, {"cell_type": "code", "execution_count": null, "metadata": {"colab": {"base_uri": "https://localhost:8080/", "height": 458}, "id": "I9h18GQB-XGv", "outputId": "1b16ef5a-01d1-4880-c38a-a2b021ac88cc"}, "outputs": [{"data": {"image/png": "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", "text/plain": ["<Figure size 393.701x393.701 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["###\n", "### Useful code for plotting figures\n", "###\n", "import matplotlib.pyplot as plt\n", "import numpy as np\n", "\n", "x = np.linspace(0,100,101)\n", "y = x + np.random.normal(0,2,101)\n", "\n", "### Make figure and axes handles\n", "figWidth = 10.  # In cm\n", "figHeight = 10. # In cm\n", "fig = plt.figure(figsize=tuple(np.array((figWidth, figHeight))/2.54)); ax = plt.axes()\n", "\n", "### Plot the data\n", "plt.plot(x, y, linewidth=1.0, color=[1, 0.5, 0.8])\n", "\n", "### Label the axes\n", "plt.xlabel(\"Independent Variable\")\n", "plt.ylabel(\"Dependent Variable\")\n", "\n", "### Add a title if necessary\n", "plt.title(\"Plot title\")\n", "\n", "### Specify the range in which data is plotted (x and y limits)\n", "ax.set_ylim(ymin=0.0, ymax=105.0)\n", "ax.set_xlim(xmin=0.0, xmax=100.0)\n", "\n", "### Specify where the tickmarks appear\n", "ax.xaxis.set_ticks((0,100));\n", "ax.yaxis.set_ticks([0.0,50.0,100.0]);\n", "\n", "### Include a figure caption\n", "txt=\"Figure 3. Y is a linear function of X with Guassian noise sampled from a distribution with zero mean and standard deviation of 2.\"\n", "plt.figtext(0.5, -0.12, txt, wrap=True, horizontalalignment='center', fontsize=10)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Inserting images for a figure (*not using* Google Colab)\n", "Some figures, e.g. the schematic of your neural network architecture, or any other diagrams, may be created in other software and imported. To do this, put the image file with file name `filename.foo` in the same directory as your ipynb file. Then enter this text into the Markdown cell to create the figure with a figure caption:\n", "\n", "```\n", "<figure><center><img src=\"filename.foo\" width=123><figcaption> Figure X. Some description of your figure. </figcaption></center></figure>\n", "```\n", "which can produce a figure like this:\n", "<figure><center><img src=\"figure.jpg\" width=500><figcaption> Figure 1. Dropout is more effective in convolutional layers when applied to whole activation maps. </figcaption></center></figure>\n", "\n", "IMPORTANT: when submitting the zip file to canvas, ***please do not use any subdirectories within the zip file***. \n", "\n", "IMPORTANT: in order for the word counter to ignore markdown used to generate figures, please make sure that all code used to generate a figure (i.e. code between and including the `<figure>` and `</figure>` tags) ***is on the same line***.\n", "\n", "### Inserting images for a figure (*using* Google Colab)\n", "***If using Google Colab*** to complete your assignment, including images as a figure is a bit more involved. ***Please use the following method***, which will make it easier for me to view your figures in other software. \n", "1) Drag and drop the image into the runtime working directory. As an example, Figure 2 below shows the image file `figure.png` in the runtime directory (where the yellow arrow points in the picture below). Note, you will receive the warning: \"Ensure that your files are saved elsewhere. This runtime's files will be deleted when this runtime is terminated.\" This means you will have to drag and drop all images you use each time you reconnect to the runtime, which is quick and easy to do. ***Make sure the same images are included in the zip file you upload to Canvas***, otherwise your code won't be able to read them and they won't display when someone marks your work.\n", "<figure><center><img src=\"colab_figures.jpg\" width=1200><figcaption> Figure 2. How to include images for figures if using Google Colab. </figcaption></center></figure>\n", "2) Create a code cell and enter the following code, which reproduces Figure 1, replacing the filename and the caption text:\n", "\n", "```\n", "import matplotlib.pyplot as plt\n", "# Read in the image\n", "im=plt.imread('/content/figure.jpg')\n", "# Setup a figure\n", "figureSize = 5\n", "fig = plt.figure(figsize=[figureSize, figureSize])\n", "# Show the image in the figure and remove axes.\n", "plt.imshow(im); plt.axis('off'); plt.show();\n", "# Setup a new figure for the caption.\n", "fig = plt.figure(figsize=[0.01,0.01]); plt.axis('off');\n", "# Write the caption here\n", "captionText = 'Figure 1. Dropout is more effective in convolutional \\\n", "layers when applied to whole activation maps.'\n", "# Show the caption text.\n", "plt.figtext(0.1, 0.1, captionText); plt.show()\n", "```\n", "3) Note that the path of the image file begins with `/content/...`, which is the name of the runtime directory in Colab. Before you submit your work on <PERSON><PERSON>, please remove the \"`/content/`\" part of the file path so that, when someone is marking your work, the code looks for the image in the correct directory.\n", "3) Adding images for figures in this way is the only time you should add a code cell that doesn't immediately follow another code cell. To continue entering text, create a Markdown (text) cell after this code cell. The next three cells implement this method so that you can see it working in practice."]}, {"cell_type": "markdown", "metadata": {}, "source": ["### ************************************************************\n", "### Example of how to include images for figures in Google Colab\n", "Here is some text that refers to Figure 1."]}, {"cell_type": "code", "execution_count": 5, "metadata": {}, "outputs": [{"data": {"image/png": "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", "text/plain": ["<Figure size 700x700 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"image/png": "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", "text/plain": ["<Figure size 1x1 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["### Code for Figure 1 ###\n", "import matplotlib.pyplot as plt\n", "# Read in the image\n", "im=plt.imread('figure.jpg')\n", "# Setup a figure\n", "figureSize = 7\n", "fig = plt.figure(figsize=[figureSize, figureSize])\n", "# Show the image in the figure and remove axes.\n", "plt.imshow(im); plt.axis('off'); plt.show();\n", "# Setup a new figure for the caption.\n", "fig = plt.figure(figsize=[0.01,0.01]); plt.axis('off');\n", "# Write the caption here\n", "captionText = 'Figure 1. Dropout is more effective in convolutional \\\n", "layers when applied to whole activation maps.'\n", "# Show the caption text.\n", "plt.figtext(0.1, 0.1, captionText); plt.show()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["Here is some more text.\n", "### ************************************************************"]}], "metadata": {"colab": {"provenance": []}, "kernelspec": {"display_name": "Python 3", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.8.16"}}, "nbformat": 4, "nbformat_minor": 0}