# 机器学习大作业执行计划

## 项目概述
本项目包含两个主要任务：
- **Task 1**: 监督学习和强化学习问题（60分，占总分50%）
- **Task 2**: 深度神经网络回归任务（87分，占总分50%）

## Task 1 执行计划

### Subtask 1: 国际象棋走法分类问题（监督学习）
**目标**: 训练分类器判断给定棋局配置下的走法是否为"好走法"

#### 1.1 解决方案A - 决策树（Decision Trees）
- **数据准备**:
  - 使用提供的`club_games_data.csv`数据集（约166MB）
  - 从PGN格式中提取棋局状态特征
  - 定义"好走法"标准（基于游戏结果）
  - 特征工程：棋子位置、控制区域、威胁等
- **模型实现**:
  - 使用scikit-learn的DecisionTreeClassifier
  - 特征选择和预处理
  - 超参数调优（max_depth, min_samples_split等）
- **评估**:
  - 交叉验证
  - 准确率、精确率、召回率、F1分数
  - 特征重要性分析

#### 1.2 解决方案B - 朴素贝叶斯或K近邻（Naive Bayes/KNN）
- **选择**: 使用朴素贝叶斯（更适合分类特征）
- **实现**:
  - 使用scikit-learn的GaussianNB或MultinomialNB
  - 特征离散化处理
  - 概率阈值调优
- **对比分析**:
  - 与决策树性能对比
  - 计算复杂度分析
  - 可解释性比较

### Subtask 2: 奇偶游戏最优策略（强化学习）
**目标**: 在给定图上找到奇偶游戏的最优策略

#### 2.1 解决方案A - 基于模型的学习（值迭代或策略迭代）
- **问题建模**:
  - 图表示：邻接矩阵
  - 状态空间：顶点集合
  - 动作空间：可行边
  - 奖励函数：基于奇偶条件
- **算法实现**:
  - 值迭代算法
  - 收敛条件设定
  - 最优策略提取
- **测试**:
  - 创建测试图实例
  - 验证策略正确性

#### 2.2 解决方案B - 无模型学习（SARSA或Q-Learning）
- **选择**: Q-Learning
- **实现**:
  - Q表初始化
  - ε-贪心探索策略
  - 学习率和折扣因子调优
  - 经验回放（可选）
- **对比分析**:
  - 收敛速度比较
  - 样本效率分析
  - 策略质量评估

### Task 1 文件结构
```
Task 1/
├── XXXXXX.ipynb          # 主要代码实现
└── XXXXXX.pdf            # 分析报告（1000字限制）
```

## Task 2 执行计划

### 数据生成和模型基础
- **数据函数**: y = 0.2(x-3)² + 0.2ξ
- **模型架构**: 3层隐藏层MLP，每层16个神经元
- **激活函数**: ReLU（隐藏层），无激活（输出层）

### 子任务详细计划

#### Subtask 1A (5分): MLP类实现
- 使用PyTorch创建MLP类
- 支持1D和2D输入
- 前向传播函数实现
- 详细代码注释

#### Subtask 1B (3分): ReLU vs Sigmoid分析
- 梯度消失问题
- 计算效率
- 稀疏激活特性

#### Subtask 1C (2分): 损失函数选择
- 分析数据生成过程
- 推荐MSE损失函数
- 理论依据说明

#### Subtask 1D (8分): 优化器比较
- SGD vs Adam
- 各自优缺点分析
- 适用场景讨论

#### Subtask 1E (6分): 批处理概念
- 批大小定义
- 最小/最大批大小
- 小批量vs大批量优势

#### Subtask 1F (16分): 1D输入过拟合实验
- 至少5种训练集大小
- 每种大小至少5次随机种子
- 学习曲线绘制
- 强/中/弱过拟合展示

#### Subtask 1G (6分): 结果分析
- 描述1F实验结果
- 确定无过拟合最小数据集大小
- 训练/测试损失差异解释

#### Subtask 1H (13分): 2D输入过拟合实验
- 重复1F实验，使用2D输入
- 包含1F中无过拟合的最小数据集大小
- 学习曲线对比

#### Subtask 1I (6分): 维度影响分析
- 比较1D vs 2D结果
- 过拟合程度变化
- 理论解释

#### Subtask 1J (16分): 权重衰减正则化
- 添加权重衰减到MLP
- 至少5种衰减率
- 过拟合到无过拟合转变展示

#### Subtask 1K (6分): 权重衰减效果分析
- 描述衰减率影响
- 其他观察到的效果
- 原因分析

### Task 2 技术要求
- 使用PyTorch框架
- 所有图表必须由代码生成
- 详细代码注释
- 字数限制：1250字

## 实施时间线

### 第一阶段：Task 1 数据准备和基础实现（2-3天）
1. 分析chess数据集结构
2. 实现特征提取函数
3. 创建基础的决策树和朴素贝叶斯模型
4. 实现奇偶游戏环境

### 第二阶段：Task 1 算法完善和实验（2-3天）
1. 完善监督学习模型
2. 实现强化学习算法
3. 进行对比实验
4. 撰写PDF报告

### 第三阶段：Task 2 神经网络实验（3-4天）
1. 实现MLP类
2. 完成所有子任务实验
3. 生成学习曲线和分析图表
4. 撰写分析文本

### 第四阶段：整理和提交（1天）
1. 代码清理和注释完善
2. 最终测试和验证
3. 文件组织和打包
4. 提交前检查

## 关键技术栈
- **Python**: 主要编程语言
- **PyTorch**: 深度学习框架
- **scikit-learn**: 传统机器学习
- **NumPy/Pandas**: 数据处理
- **Matplotlib**: 可视化
- **Jupyter Notebook**: 开发环境

## 风险和挑战
1. **数据处理复杂性**: Chess PGN格式解析
2. **特征工程**: 有效特征提取
3. **实验设计**: 合理的超参数选择
4. **时间管理**: 平衡两个任务的时间分配
5. **字数限制**: 在限制内提供充分分析

## 成功标准
- 所有代码能够正确运行
- 实验结果清晰展示预期现象
- 分析报告逻辑清晰、有理有据
- 符合所有格式和字数要求
- 代码注释详细，易于理解
