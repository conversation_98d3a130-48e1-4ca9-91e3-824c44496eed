# 机器学习大作业执行计划

## 项目概述
本项目包含两个主要任务：
- **Task 1**: 监督学习和强化学习问题（60分，占总分50%）
- **Task 2**: 深度神经网络回归任务（87分，占总分50%）

## Task 1 执行计划

### Subtask 1: 国际象棋走法分类问题（监督学习）
**目标**: 训练分类器判断给定棋局配置下的走法是否为"好走法"

#### 1.1 解决方案A - 决策树（Decision Trees）🏆
- **高级特征工程**（类Kaggle策略）:
  - 使用提供的`club_games_data.csv`数据集（约166MB）
  - **多层次特征提取**：
    - 基础特征：棋子位置、材料平衡、王安全性
    - 战术特征：叉子、牵制、发现攻击检测
    - 位置特征：中心控制、空间优势、弱格分析
    - 时间特征：开局/中局/残局阶段识别
  - **特征交互**：创建特征组合和多项式特征
  - **目标工程**：基于ELO评级差异和游戏结果的智能标签
- **模型实现**:
  - 使用scikit-learn的DecisionTreeClassifier
  - **高级调优**：网格搜索 + 交叉验证
  - **特征重要性驱动的迭代优化**
  - 树可视化和解释性分析
- **评估**:
  - 分层交叉验证（按ELO等级分层）
  - 多指标评估：AUC-ROC, Precision-Recall曲线
  - **错误分析**：分析误分类案例的模式

#### 1.2 解决方案B - K近邻（KNN）🎯
- **选择理由**: KNN更适合chess位置的相似性匹配
- **高级实现**:
  - **智能距离度量**：
    - 棋盘位置的曼哈顿距离
    - 加权欧几里得距离（基于特征重要性）
    - 自定义chess-specific距离函数
  - **维度优化**：
    - PCA降维 + 特征选择
    - 局部敏感哈希(LSH)加速近邻搜索
  - **超参数优化**：
    - K值的智能选择（奇数，避免平票）
    - 距离权重策略优化
- **对比分析**:
  - **互补性分析**：DT的规则 vs KNN的相似性
  - **计算复杂度权衡**：训练时间 vs 预测时间
  - **可解释性对比**：规则提取 vs 近邻案例分析
  - **鲁棒性测试**：噪声数据和异常值的影响

### Subtask 2: 奇偶游戏最优策略（强化学习）✅ **已完成**
**目标**: 在给定图上找到奇偶游戏的最优策略

#### 2.1 解决方案A - Value Iteration（基于模型）✅ **成功实现**
- **问题建模**:
  - ✅ 图表示：邻接字典结构，支持有向图
  - ✅ 状态空间：5个顶点的测试游戏
  - ✅ 动作空间：每个顶点的可行后继状态
  - ✅ 奖励函数：基于优先级偏好的奖励机制
- **算法实现**:
  - ✅ 值迭代算法：标准Bellman方程实现
  - ✅ 收敛条件：基于值变化阈值(1e-6)
  - ✅ 最优策略提取：从值函数导出贪婪策略
  - ✅ 获胜区域分析：基于值函数阈值划分
- **优化历程**:
  - ✅ **问题1解决**：修复立即收敛问题（改进游戏结构和初始化）
  - ✅ **问题2解决**：修复值爆炸问题（移除累积奖励，使用稳定算法）
  - ✅ **最终成果**：2次迭代快速收敛，值范围0.2-0.8，清晰的获胜区域

#### 2.2 解决方案B - Q-Learning（无模型）✅ **优化成功**
- **选择**: Q-Learning with systematic improvements
- **实现**:
  - ✅ Q表初始化：基于目标状态优先级的智能初始化
  - ✅ ε-贪心探索策略：带衰减的探索机制(ε=0.2→0.01)
  - ✅ 学习率和折扣因子调优：lr=0.05, γ=0.9
  - ✅ 系统性探索：均匀访问所有状态替代随机探索
- **关键优化**:
  - ✅ **奖励函数对齐**：简化并与Value Iteration逻辑一致
  - ✅ **训练策略改进**：系统性状态访问 + ε衰减
  - ✅ **参数调优**：保守参数确保稳定收敛
- **对比分析**:
  - ✅ **策略一致性提升**：从20%提升到80%（4/5状态一致）
  - ✅ **收敛速度比较**：VI 2次迭代 vs QL 3000 episodes
  - ✅ **算法特性分析**：确定性vs随机性、全局vs局部最优
  - ✅ **详细差异分析**：剩余20%差异的等价动作解释

#### 2.3 **项目亮点与学术价值** 🏆
- **完整的算法开发周期**：从问题识别→调试优化→最终解决
- **深度的理论理解**：掌握收敛性质、参数影响、算法适配
- **高质量的工程实践**：模块化设计、完善注释、可重现结果
- **系统性的比较分析**：不仅比较结果，更分析差异原因
- **创新性的优化方法**：针对parity game的算法适配策略

### Task 1 文件结构
```
Task 1/
├── XXXXXX.ipynb          # 主要代码实现
└── XXXXXX.pdf            # 分析报告（1000字限制）
```

## Task 2 执行计划

### 数据生成和模型基础
- **数据函数**: y = 0.2(x-3)² + 0.2ξ
- **模型架构**: 3层隐藏层MLP，每层16个神经元
- **激活函数**: ReLU（隐藏层），无激活（输出层）

### 子任务详细计划

#### Subtask 1A (5分): MLP类实现
- 使用PyTorch创建MLP类
- 支持1D和2D输入
- 前向传播函数实现
- 详细代码注释

#### Subtask 1B (3分): ReLU vs Sigmoid分析
- 梯度消失问题
- 计算效率
- 稀疏激活特性

#### Subtask 1C (2分): 损失函数选择
- 分析数据生成过程
- 推荐MSE损失函数
- 理论依据说明

#### Subtask 1D (8分): 优化器比较
- SGD vs Adam
- 各自优缺点分析
- 适用场景讨论

#### Subtask 1E (6分): 批处理概念
- 批大小定义
- 最小/最大批大小
- 小批量vs大批量优势

#### Subtask 1F (16分): 1D输入过拟合实验
- 至少5种训练集大小
- 每种大小至少5次随机种子
- 学习曲线绘制
- 强/中/弱过拟合展示

#### Subtask 1G (6分): 结果分析
- 描述1F实验结果
- 确定无过拟合最小数据集大小
- 训练/测试损失差异解释

#### Subtask 1H (13分): 2D输入过拟合实验
- 重复1F实验，使用2D输入
- 包含1F中无过拟合的最小数据集大小
- 学习曲线对比

#### Subtask 1I (6分): 维度影响分析
- 比较1D vs 2D结果
- 过拟合程度变化
- 理论解释

#### Subtask 1J (16分): 权重衰减正则化
- 添加权重衰减到MLP
- 至少5种衰减率
- 过拟合到无过拟合转变展示

#### Subtask 1K (6分): 权重衰减效果分析
- 描述衰减率影响
- 其他观察到的效果
- 原因分析

### Task 2 技术要求 + 类Kaggle增强策略
- 使用PyTorch框架
- 所有图表必须由代码生成
- 详细代码注释
- 字数限制：1250字

#### 🚀 **类Kaggle增强策略**（在要求范围内）：
1. **高级正则化组合**：
   - 权重衰减 + Dropout + Batch Normalization
   - 早停法(Early Stopping)实现
   - 学习率调度策略

2. **实验设计优化**：
   - **更系统的超参数搜索**：
     - 学习率：[1e-4, 1e-3, 1e-2, 1e-1]
     - 权重衰减：[0, 1e-5, 1e-4, 1e-3, 1e-2]
     - 批大小：[16, 32, 64, 128]
   - **交叉验证**：K-fold验证提高结果可靠性
   - **多种初始化策略**：Xavier, He, 正态分布

3. **高级可视化**：
   - 损失曲线的置信区间
   - 学习率vs损失的热力图
   - 权重分布的演化动画
   - 梯度流分析图

4. **深度分析**：
   - **模型诊断**：
     - 梯度消失/爆炸检测
     - 权重更新幅度分析
     - 激活值分布监控
   - **泛化能力分析**：
     - 学习曲线的bias-variance分解
     - 不同数据集大小的泛化误差曲线
     - 模型复杂度vs性能的权衡分析

## 实施时间线

### 第一阶段：Task 1 数据准备和基础实现（2-3天）
1. 分析chess数据集结构
2. 实现特征提取函数
3. 创建基础的决策树和朴素贝叶斯模型
4. 实现奇偶游戏环境

### 第二阶段：Task 1 算法完善和实验（2-3天）
1. 完善监督学习模型
2. 实现强化学习算法
3. 进行对比实验
4. 撰写PDF报告

### 第三阶段：Task 2 神经网络实验（3-4天）
1. 实现MLP类
2. 完成所有子任务实验
3. 生成学习曲线和分析图表
4. 撰写分析文本

### 第四阶段：整理和提交（1天）
1. 代码清理和注释完善
2. 最终测试和验证
3. 文件组织和打包
4. 提交前检查

## 关键技术栈 + 类Kaggle工具
- **Python**: 主要编程语言
- **PyTorch**: 深度学习框架
- **scikit-learn**: 传统机器学习
- **NumPy/Pandas**: 数据处理
- **Matplotlib/Seaborn**: 高级可视化
- **Jupyter Notebook**: 开发环境

### 🏆 **类Kaggle增强工具包**：
- **python-chess**: 专业chess库，用于PGN解析和特征提取
- **plotly**: 交互式可视化
- **scipy**: 统计分析和优化
- **sklearn.model_selection**: 高级交叉验证策略
- **optuna**: 自动超参数优化（可选，如果时间允许）
- **tensorboard**: 训练过程可视化（PyTorch集成）

## 风险和挑战
1. **数据处理复杂性**: Chess PGN格式解析
2. **特征工程**: 有效特征提取
3. **实验设计**: 合理的超参数选择
4. **时间管理**: 平衡两个任务的时间分配
5. **字数限制**: 在限制内提供充分分析

## 成功标准
- 所有代码能够正确运行
- 实验结果清晰展示预期现象
- 分析报告逻辑清晰、有理有据
- 符合所有格式和字数要求
- 代码注释详细，易于理解
