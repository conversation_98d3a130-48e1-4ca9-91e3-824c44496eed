
Machine Learning Assessment
Sussex A Joint Institute.2025
Overview
Obiective
In this assessment, you will be given two main tasks, each corresponding to the two halves of thismodule.
Task 1
You will be asked to solve 2 problems. The first one is about supervised machine learning (SML), andthe second one is about reinforcement learning..For supervised machine learning you need to useDecision Trees (DTs) and either Naive Bayes (NBs) or k-Nearest Neighbor (KNN) to provide twosolutions, which will then be compared and analysed. Similarly, for the reinforcement learning (RL)task, you need to use a model-based learning method (either value or policy iteration) and model-freelearning method (either SARSA or Q-Learning). As for SML, you then will have to compare andanalyse the solutions provided in each case. Your answers will be contained in a Jupyter Notebook anda PDF, as indicated in each subtask. A word count will be imposed in each case.
Task 2You will be asked to perform a regression task using a deep neural network. This task will be dividedinto multiple subtasks, including i) short subtasks requiring you to answer short questions and explain atechnical detail, or critically evaluate a method used in neural network modelling, and i) longersubtasks for which you will have to write code and/or present some results. You will write all answersand code in a single Jupyter Notepook.
How to submit
Please submit your assignment as a zip file that includes all files reqyired for both tasks, The zip
file must contain two directories entitled “Task 1" and "Task 2". All files relevant for each task mustbe located within the corresponding directory in the zip file.For Task 1, this includes two files: the .ipynb file containing your code and the .pdf file containingyour written report.For Task 2, this includes the .ipynb file containing your work on the tasks, along with anyadditional image files (if any) that are used for figures (other than figures that are generated asoutput from code cells, e.g. diagrams) in your report.
For both tasks, please name each file using your candidate number, i.e. if your candidate numbeis XXXXXX, then the Jupyter Notebooks should be called XXXXXX.ipynb, and the report for Task1 should be called XXXXXX.pdf.
Please name the zip file that contains your two task directories XXXXXX.zip, where XXXXXX is
your candidate number.
Submit through Canvas Online (not Turnitin).


Marking Criteria
Task 1
Each subtask is worth 30 points, for a total of 60 points for both subtasks. The 60 points will beconverted into a percentage, concretely to add up to 50% of the total marks (the other 50% comes fromTask 2). Within each subtask (Subtask 1 is about SiML and Subtask 2 is about RL) the 30 marks will beallocated as follows: 10 marks for the first solution (using DTs for SML and model-based learning forRL), 10 marks for the second solution (using either NBs of KNN for SML and model-free learning foiRL), and 10 marks for the corresponding part in the PDF report. The Jupyter notebook must containcode that is logically correct, run appropriately, is well structured, and very well commented to aid itsunderstanding. Code without any comments will not be marked. On the other hand, the PDF reporthas a limit of 1000 words, with an expected average of 250 words per solution (i.e., ~250 words forDTs, ~250 words for NBs/KNN, ~250 words for the model-based learning solution, and ~250 words foithe model-free learning solution).
Task 2
The Notebook specifies how many marks you will be awarded for each subtask.The word limit is 1250 words. This is a hard limit: any part of your Task 2 assessment thatexceeds that limit will not be marked. Each subtask in the Notebook indicates how many marks areavailable. There are 87 marks available in total, and your final grade for this part will be converted intoa percentage. The more marks that are available for a subtask, the more work you are expected to doIn general, one mark is available per standalone component of your response. For subtasks requiringtext response in a Markdown cel, marks will be awarded for each distinct statement you make, forexample being able to recall a specific neural networks technique, describing a result from anexperiment, providing a scientifically grounded reason for an answer, etc. For subtasks requiring you towrite code and perform an experiment, marks will be awarded for including necessary computations,well structured code with good commenting, clear presentation of your results in fiqures for plottingrelevant data, and meaningful figure captions. Any code or text provided in additional files will notbe marked. Any figures used for plotting data, but have been imported as an image, rather thanbeing generated as output from a code cell, will not be marked.
Otherimportant notes
Task 1Figures and their captions do not count towards the word limit, but must be reasonably short
Task 2
You must use PyTorch to build, train, and test the NNs for this assessment. You are permitted to use anyof the functions available in PyTorch, e.g. to import and process data, to define NN layers and optimisersetc., but you are not permitted to load pre-trained NN models that have been built by others.Make sure that every figure has a corresponding caption that provides the figure name (e.g. Figure 1)and that explains the details of what that figure is showing. Make sure that figures have labelled axes. lyou are comparing multiple sets of results with each other in diferent plots, make sure that those plotsuse the same scale for the axes.


Task summary
Task 1
There are 2 subtasks (Subtask 1 and Subtask 2), with each subtask expected to be solved in two waysleading to solutions 1.A and 1.B for Subtask 1 and 2.A and 2.B for Subtask 2. Subtask 1 is aclassification problem, while Subtask 2 is a search problem on a graph.
Task 2In this task, you will be given code that mapsN-dimensional input values, x, into 1-dimensiona!output values, y, where y=0.2(x-2)+0.2ξ,,and ξ is a normallydistributed random variable with zero mean andunit variance, and the subscript i denotesindividual samples. The code generates trainingdata and test data, and the task will require you tobuild a multi-layer perceptron that performsnon-linear regression on the data. You will beasked to demonstrate how different features of thedataset and different features of the modeinfluence the performance of the regression taskproviding reasons for your results. An illustrationof the data, for 1-dimensional x, is shown in Figure

Figure 1.Example of the quadratic function, whereinput xis mapped to output y with some noise. 16 datasamples are shown as blue dots.and the noise-freequadratic function that has to be learned is shown inred.


Assignment:NeuralNetworks.A2 2024
Task details
You must include all of your assessment files in a single zip file called XXXXXX.zip, which is to besubmitted electronically via Canvas, where XXXXXX is your candidate number. All files relevant toTask 1 must be in a subdirectory of the zip file called “"Task ,”, All files relevant to Task 2 must bein a subdirectory called “Task 2". Make sure there are no subdirectories within the Task 1 or Task2 directories.
Task 1
of the.In subtask 1, you will be learninghowto make aoocmoves in the gameof chess.Thetaskclassifier is, given a game contiguratorto decide whether it is or it is not a goodandmoveYour job is to train the classifier to make suchI decisions using data available online about chess plays(any freely available dataset can be used).To use the classifier, one should input a game confiquratiorand a valid move and the machine learning system should classify the move a “good" or "bad'depending on the likelihood that such a move would eventually lead to a win. in your report you shoulcspecifically indicate how the input data is being used by the classifier to make their predictions, that iswhat are the variables being used to make the classification process. in subtask 2 you will now begiven a parity game, which is a game on a graph, and this time will be asked to find an optimal strategyin the game from a given starting node. A short and simple description of parity games is given in theattached file parity.pdf but several descriptions of parity games can be found online. In the JupytelNotebook, an implementation to solve each problem will be developed, while in the PDF report you wildescribe in as much detalls as possible, but within the word limit, the design of your solutions, that ishow you have decided to reduce the input problem both to a classification problem for a given gameconfiguration and move in subtask 1 and to a reinforcement learning problem in subtask 2 to find aroptimal strategy in the parity game, which is played on a labelled graph, for a given initial node.
Task 2You wil be asked to complete a set of subtasks. These are all provided in a Jupyter Notebook, whereyou will be given space to write code in code cells and to write text answers in markdown cells. Alresponses to the subtasks must be provided in the same Jupyter Notebook, i.e. any additionaPython files or text documents will not be marked. You must name the Notebook XXXXXX.ipynbwhere XXXXXX is your candidate number.
All figures that show results from your models must be generated from code cells. Make surethose figures are still visible when you submit your assessment. l will not run any of your code cellsto produce the output. lf necessary for your responses, you may also include images as separatefiles that are loaded into the Notebook. Guidance for how to produce figures and how to use Latexmarkup within the Notebook is provided intheseparateNotebookuseful code for figures equatons.ipynb.
All image files and the Notebook must be placed in the directory named “Task 2” in thesubmitted zip file.
When writing responses in markdown cells, you must use the cells with text in italics that says"Replace this text with your response". Use these cells for writing your answers, unless you areasked to use a code cell.
Remember that the task is not to produce a neural network that displays the best possibleperformance. What we are looking for is your scientific scrutiny of the model and the technigues youhave employed, and your ability to demonstrate your understanding of the principles covered in thelectures and labs.


Word counts
Task 1For the PDF file there is a word limit of 1000 words, roughly structured and subdivided as explainec
before in the marking criteria.
Task 2Your responses in Markdown cells must not exceed a word limit of 1250 words in total, asdetermined by the Python script, count jupyter nb words.py, that is provided with thisassessment. An example of how to use count jupyter nb words.py is provided as preamblecomments within the file. When writing your responses, remember to delete the text "Replace this texiwith your response", otherwise it wil contribute to your word count. None of the other text, either incode cells, or in markdown cells that describe each subtask, will contribute to the word count. Thescript outputs the number of words you have added to the document. lf you exceed the word count, thescript will also output a short string, starting from where the word count is exceeded, so that you carlocate where that happens. Any part of the Markdown cells that exceeds 1250 words will not bemarked.
Code
Task 1All code must run and be logically correct, with comments that help understand the logic of such code.
Task 2All code to build, run, and analyse your models, as well as to plot results, must be provided in the codecells provided in the .ipynb file. lf you need to create new code cells for a given subtask, you may do soimmediately after the provided code cell. lmportant: l will not run any of your code. Therefore, alfigures must be generated and visible in the Notebook when you submit to Canvas, or you willmiss available marks. ln order for me to determine whether or not you have correctly completed theexperiments, l may need to look at your code. To help me make sense of your code, and therefore tohelp me improve my feedback to you, please provide plenty of comprehensible commentsincluding general overviews for each section of code, and shorter comments every few lines of code (inot on every line). You will receive some marks for good commenting of your code.
Important to rememberYour work will be assessed by humans who get tired/hungry/frustrated/intrigued/curious. While we makeevery effort to assess your work fairly, we are not perfect! Help us to assess your work, and to realisehow talented you really are, by making your presentation clear and concise. Check that references tofigures are consistent, check for spelling, grammar, and consistent terminology. Every little helps
Use of generative Al
You are only permited to use generative Al as a tool to aid debugging code and to suggest approachesto implementing code. You are not permited to use generative Al to write the entirety of your code, or towrite your text-based answers. lf you have any questions about what is and is not permited use ofgenerative Al, please email me for clarification, <EMAIL> given the subtasks (and paraphrased versions) to diferent generative Al tools (e.g. chatGP?Gemini, Copilot etc.) and generated numerous responses from it. l therefore have a good idea of the kincof content it will produce for this assignment. Be very cautious about what these tools provideRemember: these are generative models, which meansthatthey have been trained to give responses that prioritise appearing plausible over being factually correct. lt is not difficult to getgenerative Al to generate factually incorrect responses about technical details.
Further advice for preparing your submissionMake sure you include any image files in the correct directory of your zip file, if they are needed for yournotebooks. Make sure that those image files are correctly referenced in your Notebook. lf you have usedGoogle Colab and have followed the guidance (provided inuseful code for figures equatons . ipynb) for including images in figures, then be sure toremove any occurrences of “/content!" in the file path, otherwise they will not show in your submittedNotebook.




[![]()](https://file+.vscode-resource.vscode-cdn.net/c%3A/Users/<USER>/.vscode/extensions/cweijan.vscode-office-3.5.4/resource/pdf/#page=1 "页码 1")[![]()](https://file+.vscode-resource.vscode-cdn.net/c%3A/Users/<USER>/.vscode/extensions/cweijan.vscode-office-3.5.4/resource/pdf/#page=2 "页码 2")

这是task1里面的第二部分的要求

Parity GamesParity games are two-player infinite-duration games played on directed graphs. They are a central topic in logic, automata theory, and formal verification, especially in the analysis of modal μ-calculus and model checking.Game StructureA parity game is defined by a tuple:G = (V, V₀, V₁, E, Ω)where:-V is a finite set of vertices (or positions).-V₀ ⊆ V and V₁ ⊆ V partition V, representing positions controlled by Player 0 and Player 1, respectively.-E ⊆ V × V is the set of directed edges, defining how the game progresses.-Ω: V → ℕis a priority function assigning a non-negative integer (called a priority) to each vertex.GameplayA token is placed on an initial vertex v₀ ∈ V.If the token is on a vertex in V₀, Player 0 chooses the next move; if it is on a vertex in V₁, Player 1 chooses.This process continues indefinitely, producing an infinite path (called a play):v₀, v₁, v₂, ...such that (vᵢ, vᵢ₊₁) ∈ E for all i ≥ 0.Winning ConditionEach infinite play is evaluated based on the sequence of visited priorities. The parity condition is:Player 0 wins a play if the highest priority that occurs infinitely often in the play is even. Otherwise, Player 1 wins.This condition is prefix-independent and ω-regular.

StrategiesA strategy for a player is a rule that determines their next move based only on the history of the play so far. Formally, a strategy for Player i is a function:σᵢ: V*Vᵢ→ Vthat selects the next vertex whenever the play ends in a vertex controlled by Player i.A strategy is memoryless (or positional) if it depends only on the current vertex, not the entire history. That is:σᵢ: Vᵢ→ VOptimal (Winning) StrategiesA strategy σᵢfor Player i is called a winning strategy from a vertex v if every play starting from v and conforming to σᵢis winning for Player i, regardless of the opponent's actions.Key result: Parity games are determined and both players have memoryless winning strategies from their respective winning regions. This means:-From each vertex, either Player 0 or Player 1 has a memoryless strategy to force a win.-The set of vertices can be partitioned into the winning region of Player 0, denoted by W0, and the winning region of Player 1, denoted by W1.Representation of Games and(Optimal/Winning)StrategiesA parity game can be represented with the adjacency matrix of the graphwhere the game is played, along with a vector of pairs(pl,pr)indicating the player pl that owns the vertex and the priority pr of every vertex in the graph. Strategies, and optimal strategies in particular, canbe represented with a vector of pairs (v,v’)indicating the choice v’that a player would make when at vertex v, since (optimal)strategies can be assumed to be memoryless.
